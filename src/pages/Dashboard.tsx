import { Wrench } from "lucide-react";
import { HeroSection } from "@/components/home/<USER>";
import { FeatureSection } from "@/components/home/<USER>";
import { CTASection } from "@/components/home/<USER>";
import {
  MetricCard,
  StatusBreakdown,
  RankingCard,
  BarChartCard,
  CircularDiagram
} from "@/components/dashboard";

export function Dashboard() {
  return (
    <main>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <MetricCard
          title="Active Work Orders"
          value={10}
          trend={{ value: 5, label: "vs last week", positive: true }}
          icon={<Wrench className="h-4 w-4" />}
        />
        <StatusBreakdown
          title="Work Order Status"
          items={[
            { label: "Open", count: 10, variant: "default" },
            { label: "In Progress", count: 5, variant: "secondary" },
            { label: "Ready to Close", count: 2, variant: "destructive" },
            { label: "Completed", count: 1, variant: "outline" },
          ]}
          total={18}
        />
        <RankingCard
          title="Top Technicians"
          items={[
            { name: "<PERSON>", label: "10 orders • 20h", count: "90%" },
            { name: "<PERSON>", label: "8 orders • 16h", count: "85%" },
            { name: "Bob Smith", label: "6 orders • 12h", count: "80%" },
          ]}
        />
      </div>
      <div className="mt-6 grid grid-cols-2">
        <BarChartCard
          title="Work Orders Over Time"
          description="Last 30 days"
          chartConfig={{ label: "Work Orders", color: "#33ffff" }}
          data={[
            { period: "1", count: 10 },
            { period: "2", count: 12 },
            { period: "3", count: 8 },
            { period: "4", count: 15 },
            { period: "5", count: 10 },
            { period: "6", count: 12 },
            { period: "7", count: 8 },
            { period: "8", count: 15 },
            { period: "9", count: 10 },
            { period: "10", count: 8 }
          ]}
        />
        <CircularDiagram
          title="Invoice Status"
          description="Last 30 days"
          statusData={[
            { name: "Paid", value: 10, color: "#4ade80" },
            { name: "Unpaid", value: 5, color: "#facc15" },
            { name: "Partially Paid", value: 2, color: "#f87171" },
          ]}
        />
      </div>
      {/* <HeroSection />
      <FeatureSection />
      <CTASection /> */}
    </main>
  )
}