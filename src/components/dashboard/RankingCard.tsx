import { <PERSON>, Card<PERSON>eader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export interface RankingItem {
  name: string
  label: string
  count: number | string
}

interface RankingCardProps {
  title: string
  items: RankingItem[]
  className?: string
}

export function RankingCard({ title, items, className }: RankingCardProps) {
  return (
    <Card className={cn("", className)}>
    <CardHeader>
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
    </CardHeader>
    <CardContent className="space-y-3">
      {items.map((item, index) => (
        <div key={item.name} className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">
              {index + 1}
            </Badge>
            <div>
              <p className="text-sm font-medium">{item.name}</p>
              <p className="text-xs text-muted-foreground">
                {item.label}
              </p>
            </div>
          </div>
          <Badge variant="default">{item.count}</Badge>
        </div>
      ))}
    </CardContent>
  </Card>
  )
}